import java.util.Scanner;
import java.io.PrintWriter;
import java.io.FileNotFoundException;

public class Main {
    public static void main(String[] args) {
        try {
            // read from input.txt
            Scanner sc = new Scanner(System.in);

            // write to output.txt
            PrintWriter out = new PrintWriter(System.out);

            // example: read an integer and a string, then print them
            int n = sc.nextInt();     // first number from input.txt
            String name = sc.next();  // second token (word) from input.txt

            out.println("Namaste " + name + "!");
            out.println("You entered number: " + n);

            sc.close();
            out.close();
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
}
